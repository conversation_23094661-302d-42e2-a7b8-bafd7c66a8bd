"""
Visualization Module

This module contains the GUI and visualization components for the Basal Ganglia
Oscillator simulation, including real-time plotting of neural activity and
interactive controls.

Classes:
    NetworkVisualizer: Pygame-based visualization of the bipartite network
"""

import pygame
import pygame_gui
import numpy as np
from collections import deque
import matplotlib.cm as cm
import matplotlib.colors as mcolors
from network import BipartiteNetwork


class NetworkVisualizer:
    """
    Pygame-based visualization of the bipartite network.
    
    This class provides a comprehensive GUI for visualizing neural network activity,
    including raster plots, weight matrices, population firing rates, and interactive
    controls for simulation parameters.
    
    Attributes:
        width (int): Window width in pixels
        height (int): Window height in pixels
        screen (pygame.Surface): Main display surface
        ui_manager (pygame_gui.UIManager): GUI manager for controls
        network (BipartiteNetwork): The neural network being visualized
        spike_history_A (deque): History of spikes from Layer A
        spike_history_B (deque): History of spikes from Layer B
        rate_history_A (deque): History of firing rates for Layer A
        rate_history_B (deque): History of firing rates for Layer B
        paused (bool): Whether simulation is paused
    """
    
    def __init__(self, width=1600, height=800):
        """
        Initialize the network visualizer with specified window dimensions.
        
        Args:
            width (int, optional): Window width in pixels. Defaults to 1600.
            height (int, optional): Window height in pixels. Defaults to 800.
        """
        pygame.init()
        pygame.font.init()
        
        # --- Theme and Colors ---
        self.THEME = {
            "background": (20, 20, 30), 
            "panel_bg": (40, 40, 55),
            "text": (230, 230, 230), 
            "text_dark": (150, 150, 150),
            "border": (80, 80, 100), 
            "grid": (60, 60, 75),
            "layer_a": (255, 80, 80), 
            "layer_b": (80, 120, 255),
            "font_s": pygame.font.Font(None, 20),
            "font_m": pygame.font.Font(None, 24),
            "font_l": pygame.font.Font(None, 32)
        }
        
        self.width = width
        self.height = height
        self.screen = pygame.display.set_mode((width, height))
        pygame.display.set_caption("Bipartite Spiking Neural Network with STDP")
        
        self.ui_manager = pygame_gui.UIManager((width, height), 'theme.json')
        self.network = BipartiteNetwork(n_A=80, n_B=100)
        
        # --- Data History for Plots ---
        self.raster_window = 2000  # ms
        self.spike_history_A = deque()
        self.spike_history_B = deque()
        
        self.rate_window = 500  # ms
        self.rate_history_len = 200  # Number of points to store
        self.rate_history_A = deque(maxlen=self.rate_history_len)
        self.rate_history_B = deque(maxlen=self.rate_history_len)
        self.rate_update_interval = 50  # ms
        self.last_rate_update = 0

        self._setup_layout()
        self._create_ui_elements()
        self._create_colormaps()

        self.clock = pygame.time.Clock()
        self.paused = False

    def _setup_layout(self):
        """Define the geometry of all UI panels."""
        self.controls_panel = pygame.Rect(10, 10, 380, self.height - 20)  # Expanded width for lognormal controls
        self.activity_panel = pygame.Rect(400, 10, 570, self.height - 20)  # Adjusted position
        self.weights_panel = pygame.Rect(980, 10, 610, self.height - 20)

        # Sub-rects for raster plots
        self.raster_A_rect = pygame.Rect(
            self.activity_panel.x + 40, 
            self.activity_panel.y + 80, 
            self.activity_panel.width - 60, 
            (self.activity_panel.height - 150) // 2
        )
        self.raster_B_rect = pygame.Rect(
            self.activity_panel.x + 40, 
            self.raster_A_rect.bottom + 50,
            self.activity_panel.width - 60, 
            (self.activity_panel.height - 150) // 2
        )

        # Sub-rects for weight matrices
        self.matrix_BA_rect = pygame.Rect(
            self.weights_panel.x + 10, 
            self.weights_panel.y + 140,
            self.network.n_B * 3, 
            self.network.n_A * 3
        )
        self.matrix_AB_rect = pygame.Rect(
            self.matrix_BA_rect.left,
            self.matrix_BA_rect.bottom + 80,
            self.network.n_A * 3,
            self.network.n_B * 3
        )

    def _create_ui_elements(self):
        """Create UI sliders, buttons, and labels."""
        # --- Labels ---
        pygame_gui.elements.UILabel(
            relative_rect=pygame.Rect(20, 20, 360, 30),
            text="CONTROLS & PARAMETERS",
            manager=self.ui_manager,
            object_id='@title_label'
        )
        self.sparsity_label = pygame_gui.elements.UILabel(
            relative_rect=pygame.Rect(20, 70, 280, 20),
            text=f'Connection Prob: {self.network.connection_prob:.2f}',
            manager=self.ui_manager
        )
        self.rate_A_label = pygame_gui.elements.UILabel(
            relative_rect=pygame.Rect(20, 140, 280, 20),
            text=f'Background Rate A: {self.network.background_rate_A:.1f} Hz',
            manager=self.ui_manager
        )
        self.rate_B_label = pygame_gui.elements.UILabel(
            relative_rect=pygame.Rect(20, 210, 280, 20),
            text=f'Background Rate B: {self.network.background_rate_B:.1f} Hz',
            manager=self.ui_manager
        )

        # --- Lognormal Distribution Labels ---
        pygame_gui.elements.UILabel(
            relative_rect=pygame.Rect(20, 280, 360, 25),
            text="LOGNORMAL WEIGHT DISTRIBUTION",
            manager=self.ui_manager,
            object_id='@section_label'
        )

        # W_BA (Excitatory) parameters
        pygame_gui.elements.UILabel(
            relative_rect=pygame.Rect(20, 315, 170, 20),
            text="W_BA (Excitatory):",
            manager=self.ui_manager,
            object_id='@subsection_label'
        )
        self.mu_BA_label = pygame_gui.elements.UILabel(
            relative_rect=pygame.Rect(20, 345, 170, 20),
            text=f'μ (Mean): {self.network.lognorm_mu_BA:.2f}',
            manager=self.ui_manager
        )
        self.sigma_BA_label = pygame_gui.elements.UILabel(
            relative_rect=pygame.Rect(20, 395, 170, 20),
            text=f'σ (Std Dev): {self.network.lognorm_sigma_BA:.2f}',
            manager=self.ui_manager
        )
        self.scale_BA_label = pygame_gui.elements.UILabel(
            relative_rect=pygame.Rect(20, 445, 170, 20),
            text=f'Scale: {self.network.lognorm_scale_BA:.3f}',
            manager=self.ui_manager
        )

        # W_AB (Inhibitory) parameters
        pygame_gui.elements.UILabel(
            relative_rect=pygame.Rect(200, 315, 170, 20),
            text="W_AB (Inhibitory):",
            manager=self.ui_manager,
            object_id='@subsection_label'
        )
        self.mu_AB_label = pygame_gui.elements.UILabel(
            relative_rect=pygame.Rect(200, 345, 170, 20),
            text=f'μ (Mean): {self.network.lognorm_mu_AB:.2f}',
            manager=self.ui_manager
        )
        self.sigma_AB_label = pygame_gui.elements.UILabel(
            relative_rect=pygame.Rect(200, 395, 170, 20),
            text=f'σ (Std Dev): {self.network.lognorm_sigma_AB:.2f}',
            manager=self.ui_manager
        )
        self.scale_AB_label = pygame_gui.elements.UILabel(
            relative_rect=pygame.Rect(200, 445, 170, 20),
            text=f'Scale: {self.network.lognorm_scale_AB:.3f}',
            manager=self.ui_manager
        )

        self.time_label = pygame_gui.elements.UILabel(
            relative_rect=pygame.Rect(20, self.controls_panel.height - 100, 280, 20),
            text="Time: 0.00 s",
            manager=self.ui_manager
        )
        self.fps_label = pygame_gui.elements.UILabel(
            relative_rect=pygame.Rect(20, self.controls_panel.height - 70, 280, 20),
            text="FPS: 0",
            manager=self.ui_manager
        )

        # --- Sliders ---
        self.sparsity_slider = pygame_gui.elements.UIHorizontalSlider(
            relative_rect=pygame.Rect(20, 100, 280, 25),
            start_value=self.network.connection_prob,
            value_range=(0.0, 1.0),
            manager=self.ui_manager
        )
        self.rate_A_slider = pygame_gui.elements.UIHorizontalSlider(
            relative_rect=pygame.Rect(20, 170, 280, 25),
            start_value=self.network.background_rate_A,
            value_range=(0.0, 50.0),
            manager=self.ui_manager
        )
        self.rate_B_slider = pygame_gui.elements.UIHorizontalSlider(
            relative_rect=pygame.Rect(20, 240, 280, 25),
            start_value=self.network.background_rate_B,
            value_range=(0.0, 50.0),
            manager=self.ui_manager
        )

        # --- Lognormal Parameter Sliders ---
        # W_BA (Excitatory) sliders
        self.mu_BA_slider = pygame_gui.elements.UIHorizontalSlider(
            relative_rect=pygame.Rect(20, 370, 170, 20),
            start_value=self.network.lognorm_mu_BA,
            value_range=(-3.0, 1.0),
            manager=self.ui_manager
        )
        self.sigma_BA_slider = pygame_gui.elements.UIHorizontalSlider(
            relative_rect=pygame.Rect(20, 420, 170, 20),
            start_value=self.network.lognorm_sigma_BA,
            value_range=(0.1, 2.0),
            manager=self.ui_manager
        )
        self.scale_BA_slider = pygame_gui.elements.UIHorizontalSlider(
            relative_rect=pygame.Rect(20, 470, 170, 20),
            start_value=self.network.lognorm_scale_BA,
            value_range=(0.01, 1.0),
            manager=self.ui_manager
        )

        # W_AB (Inhibitory) sliders
        self.mu_AB_slider = pygame_gui.elements.UIHorizontalSlider(
            relative_rect=pygame.Rect(200, 370, 170, 20),
            start_value=self.network.lognorm_mu_AB,
            value_range=(-3.0, 1.0),
            manager=self.ui_manager
        )
        self.sigma_AB_slider = pygame_gui.elements.UIHorizontalSlider(
            relative_rect=pygame.Rect(200, 420, 170, 20),
            start_value=self.network.lognorm_sigma_AB,
            value_range=(0.1, 2.0),
            manager=self.ui_manager
        )
        self.scale_AB_slider = pygame_gui.elements.UIHorizontalSlider(
            relative_rect=pygame.Rect(200, 470, 170, 20),
            start_value=self.network.lognorm_scale_AB,
            value_range=(0.01, 1.0),
            manager=self.ui_manager
        )

        # --- Buttons ---
        self.reset_button = pygame_gui.elements.UIButton(
            relative_rect=pygame.Rect(20, 500, 110, 35),
            text='Reset',
            manager=self.ui_manager
        )
        self.pause_button = pygame_gui.elements.UIButton(
            relative_rect=pygame.Rect(140, 500, 110, 35),
            text='Pause',
            manager=self.ui_manager
        )
        self.regenerate_weights_button = pygame_gui.elements.UIButton(
            relative_rect=pygame.Rect(260, 500, 110, 35),
            text='Regenerate\nWeights',
            manager=self.ui_manager
        )

    def _create_colormaps(self):
        """Generate RGB arrays for colormaps to be used in heatmaps."""
        self.cmap_hot = cm.get_cmap('hot')
        self.cmap_cool = cm.get_cmap('cool')

    def _draw_panel(self, rect, title):
        """Helper to draw a styled panel background and title."""
        pygame.draw.rect(self.screen, self.THEME["panel_bg"], rect, border_radius=5)
        pygame.draw.rect(self.screen, self.THEME["border"], rect, 1, border_radius=5)
        title_surf = self.THEME["font_m"].render(title, True, self.THEME["text"])
        self.screen.blit(title_surf, (rect.x + 10, rect.y + 10))

    def draw_raster_plot(self, rect, spike_history, n_neurons, color, title):
        """Draw a rich raster plot for a neural population."""
        # --- Axis labels ---
        y_axis_label = self.THEME["font_s"].render("Neuron ID", True, self.THEME["text_dark"])
        self.screen.blit(
            pygame.transform.rotate(y_axis_label, 90),
            (rect.x - 30, rect.centery - y_axis_label.get_width()//2)
        )

        # --- Plot Title ---
        title_surf = self.THEME["font_m"].render(title, True, color)
        self.screen.blit(title_surf, (rect.x, rect.y - 30))

        # --- Grid lines ---
        for i in range(0, n_neurons, 20):
            y = rect.y + (i / n_neurons) * rect.height
            pygame.draw.line(self.screen, self.THEME["grid"], (rect.x, y), (rect.right, y), 1)

        # --- Time Axis ---
        current_time = self.network.current_time
        start_time = max(0, current_time - self.raster_window)
        for i in range(5):
            t = start_time + i * self.raster_window / 4
            x = rect.x + (t - start_time) / self.raster_window * rect.width
            pygame.draw.line(self.screen, self.THEME["grid"], (x, rect.top), (x, rect.bottom), 1)
            time_text = self.THEME["font_s"].render(f"{t/1000:.1f}s", True, self.THEME["text_dark"])
            self.screen.blit(time_text, (x - time_text.get_width()//2, rect.bottom + 5))

        # --- Draw Spikes ---
        for spike_time, neuron_id in spike_history:
            if start_time <= spike_time <= current_time:
                x = rect.x + ((spike_time - start_time) / self.raster_window) * rect.width
                y = rect.y + (neuron_id / n_neurons) * rect.height
                if rect.collidepoint(x, y):
                     pygame.draw.line(self.screen, color, (x, y), (x, y + 2), 2)

    def draw_weight_matrix(self, rect, weights, cmap, title, clim):
        """Draw a heatmap of a weight matrix."""
        if weights.size == 0:
            return

        title_surf = self.THEME["font_m"].render(title, True, self.THEME["text"])
        self.screen.blit(title_surf, (rect.x, rect.y - 30))

        # Normalize weights for colormap
        norm = mcolors.Normalize(vmin=clim[0], vmax=clim[1])
        # Get RGBA array from colormap, convert to 8-bit RGB
        rgba_array = cmap(norm(weights.T))
        rgb_array = (rgba_array[:, :, :3] * 255).astype(np.uint8)

        # Create surface, blit the array, and scale to fit the rect
        matrix_surf = pygame.Surface(rgb_array.shape[:2])
        pygame.surfarray.blit_array(matrix_surf, rgb_array)
        scaled_surf = pygame.transform.scale(matrix_surf, (rect.width, rect.height))
        self.screen.blit(scaled_surf, rect)
        pygame.draw.rect(self.screen, self.THEME["border"], rect, 1)

        # --- Draw Colorbar ---
        cbar_rect = pygame.Rect(rect.right + 15, rect.y, 20, rect.height)
        for i in range(cbar_rect.height):
            # Interpolate color from bottom to top
            val = clim[0] + (clim[1] - clim[0]) * (1 - i / cbar_rect.height)
            color = cmap(norm(val))
            pygame.draw.line(
                self.screen,
                [c*255 for c in color[:3]],
                (cbar_rect.left, cbar_rect.y + i),
                (cbar_rect.right, cbar_rect.y+i),
                1
            )

        max_label = self.THEME["font_s"].render(f"{clim[1]:.1f}", True, self.THEME["text"])
        min_label = self.THEME["font_s"].render(f"{clim[0]:.1f}", True, self.THEME["text"])
        self.screen.blit(max_label, (cbar_rect.right + 5, cbar_rect.top - max_label.get_height()//2))
        self.screen.blit(min_label, (cbar_rect.right + 5, cbar_rect.bottom - min_label.get_height()//2))

    def calculate_and_update_rates(self):
        """Calculate population firing rates and store in history."""
        if self.network.current_time - self.last_rate_update < self.rate_update_interval:
            return
        self.last_rate_update = self.network.current_time

        rate_A = sum(1 for t, _ in self.spike_history_A if self.network.current_time - t < self.rate_window)
        rate_B = sum(1 for t, _ in self.spike_history_B if self.network.current_time - t < self.rate_window)

        # Normalize by window and number of neurons
        norm_factor = (self.rate_window / 1000.0)
        avg_rate_A = (rate_A / self.network.n_A) / norm_factor if self.network.n_A > 0 else 0
        avg_rate_B = (rate_B / self.network.n_B) / norm_factor if self.network.n_B > 0 else 0

        self.rate_history_A.append(avg_rate_A)
        self.rate_history_B.append(avg_rate_B)

    def draw_rate_plots(self, base_rect):
        """Draw time-series plots of population firing rates."""
        plot_rect = pygame.Rect(base_rect.x + 20, base_rect.y + 350, base_rect.width - 40, 200)
        self._draw_panel(plot_rect, "Population Firing Rates (Hz)")

        # Draw plot area
        graph_rect = pygame.Rect(plot_rect.x + 30, plot_rect.y + 30, plot_rect.width - 40, plot_rect.height - 40)

        # Determine max rate for y-axis scaling, with a minimum value
        max_rate = max(
            max(self.rate_history_A) if self.rate_history_A else 0,
            max(self.rate_history_B) if self.rate_history_B else 0,
            10.0
        )

        # --- Draw axes and grid ---
        pygame.draw.line(
            self.screen,
            self.THEME["grid"],
            (graph_rect.left, graph_rect.bottom),
            (graph_rect.right, graph_rect.bottom),
            1
        )
        y_label = self.THEME["font_s"].render("0", True, self.THEME["text_dark"])
        self.screen.blit(y_label, (graph_rect.left - 15, graph_rect.bottom - y_label.get_height()//2))
        y_label = self.THEME["font_s"].render(f"{max_rate:.0f}", True, self.THEME["text_dark"])
        self.screen.blit(y_label, (graph_rect.left - 15, graph_rect.top - y_label.get_height()//2))

        # --- Plot data ---
        def plot_line(history, color):
            if len(history) > 1:
                points = []
                for i, rate in enumerate(history):
                    x = graph_rect.x + (i / (self.rate_history_len-1)) * graph_rect.width
                    y = graph_rect.bottom - (rate / max_rate) * graph_rect.height
                    points.append((x,y))
                pygame.draw.lines(self.screen, color, False, points, 2)

        plot_line(self.rate_history_A, self.THEME["layer_a"])
        plot_line(self.rate_history_B, self.THEME["layer_b"])

    def handle_events(self, event):
        """Handle pygame and pygame_gui events."""
        if event.type == pygame.USEREVENT:
            if event.user_type == pygame_gui.UI_HORIZONTAL_SLIDER_MOVED:
                if event.ui_element == self.sparsity_slider:
                    self.sparsity_label.set_text(f'Connection Prob: {event.value:.2f}')
                elif event.ui_element == self.rate_A_slider:
                    self.network.background_rate_A = event.value
                    self.rate_A_label.set_text(f'Background Rate A: {event.value:.1f} Hz')
                elif event.ui_element == self.rate_B_slider:
                    self.network.background_rate_B = event.value
                    self.rate_B_label.set_text(f'Background Rate B: {event.value:.1f} Hz')

                # Handle lognormal parameter sliders for W_BA (excitatory)
                elif event.ui_element == self.mu_BA_slider:
                    self.network.update_lognormal_parameters(mu_BA=event.value)
                    self.mu_BA_label.set_text(f'μ (Mean): {event.value:.2f}')
                elif event.ui_element == self.sigma_BA_slider:
                    self.network.update_lognormal_parameters(sigma_BA=event.value)
                    self.sigma_BA_label.set_text(f'σ (Std Dev): {event.value:.2f}')
                elif event.ui_element == self.scale_BA_slider:
                    self.network.update_lognormal_parameters(scale_BA=event.value)
                    self.scale_BA_label.set_text(f'Scale: {event.value:.3f}')

                # Handle lognormal parameter sliders for W_AB (inhibitory)
                elif event.ui_element == self.mu_AB_slider:
                    self.network.update_lognormal_parameters(mu_AB=event.value)
                    self.mu_AB_label.set_text(f'μ (Mean): {event.value:.2f}')
                elif event.ui_element == self.sigma_AB_slider:
                    self.network.update_lognormal_parameters(sigma_AB=event.value)
                    self.sigma_AB_label.set_text(f'σ (Std Dev): {event.value:.2f}')
                elif event.ui_element == self.scale_AB_slider:
                    self.network.update_lognormal_parameters(scale_AB=event.value)
                    self.scale_AB_label.set_text(f'Scale: {event.value:.3f}')

            elif event.user_type == pygame_gui.UI_BUTTON_PRESSED:
                if event.ui_element == self.reset_button:
                    self.network.connection_prob = self.sparsity_slider.get_current_value()
                    self.network.initialize_connections()
                    self.network.current_time = 0.0
                    self.spike_history_A.clear()
                    self.spike_history_B.clear()
                    self.rate_history_A.clear()
                    self.rate_history_B.clear()
                elif event.ui_element == self.pause_button:
                    self.paused = not self.paused
                    self.pause_button.set_text('Resume' if self.paused else 'Pause')
                elif event.ui_element == self.regenerate_weights_button:
                    # Regenerate weights with current lognormal parameters
                    self.network.initialize_connections()

    def run(self):
        """Main simulation loop."""
        running = True
        while running:
            time_delta = self.clock.tick(60) / 1000.0

            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                self.handle_events(event)
                self.ui_manager.process_events(event)

            # --- Update Simulation ---
            if not self.paused:
                # Run multiple simulation steps per frame for speed
                for _ in range(5):
                    spikes_A, spikes_B = self.network.update()
                    current_time = self.network.current_time
                    # Record spikes with a cap to prevent performance degradation
                    if len(self.spike_history_A) > 5000:
                        self.spike_history_A.popleft()
                    if len(self.spike_history_B) > 5000:
                        self.spike_history_B.popleft()
                    for neuron_id in spikes_A:
                        self.spike_history_A.append((current_time, neuron_id))
                    for neuron_id in spikes_B:
                        self.spike_history_B.append((current_time, neuron_id))

                self.calculate_and_update_rates()

            # --- Update UI and Labels ---
            self.ui_manager.update(time_delta)
            self.time_label.set_text(f"Time: {self.network.current_time/1000:.2f} s")
            self.fps_label.set_text(f"FPS: {self.clock.get_fps():.0f}")

            # --- Draw Everything ---
            self.screen.fill(self.THEME["background"])

            # Draw Panels
            self._draw_panel(self.controls_panel, "")
            self._draw_panel(self.activity_panel, "NEURAL ACTIVITY")
            self._draw_panel(self.weights_panel, "SYNAPTIC WEIGHTS")

            # Draw Plots
            self.draw_raster_plot(
                self.raster_A_rect,
                self.spike_history_A,
                self.network.n_A,
                self.THEME["layer_a"],
                "Layer A (Inhibitory)"
            )
            self.draw_raster_plot(
                self.raster_B_rect,
                self.spike_history_B,
                self.network.n_B,
                self.THEME["layer_b"],
                "Layer B (Excitatory)"
            )

            self.draw_weight_matrix(
                self.matrix_BA_rect,
                self.network.W_BA,
                self.cmap_hot,
                "Excitatory Weights (B -> A)",
                (0.0, 1.0)
            )
            self.draw_weight_matrix(
                self.matrix_AB_rect,
                self.network.W_AB,
                self.cmap_cool,
                "Inhibitory Weights (A -> B)",
                (-1.0, 0.0)
            )

            self.draw_rate_plots(self.controls_panel)

            # Draw UI on top
            self.ui_manager.draw_ui(self.screen)

            pygame.display.flip()

        pygame.quit()
